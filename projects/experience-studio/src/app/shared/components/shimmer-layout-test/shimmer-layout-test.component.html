<!-- Shimmer Layout Test Component -->
<div class="container">
  <h1 style="text-align: center; margin-bottom: 30px;">Shimmer Layout Test Implementation</h1>

  <div class="layout-selector">
    <button
      *ngFor="let layoutKey of getLayoutKeys()"
      class="layout-btn"
      [class.active]="selectedLayout === layoutKey"
      (click)="selectLayout(layoutKey)">
      {{ getLayoutLabel(layoutKey) }}
    </button>
  </div>

  <div class="layout-preview">
    <div class="layout-title">{{ getCurrentLayout().title }}</div>

    <!-- Dynamic shimmer layout container -->
    <div class="shimmer-layout-preview">
      <div class="shimmer-grid" [ngClass]="getCurrentLayout().class">
        <!-- Header -->
        <div class="shimmer-header shimmer-element">
          <div class="shimmer-logo shimmer"></div>
          <div class="shimmer-nav">
            <div class="shimmer-nav-item shimmer"></div>
            <div class="shimmer-nav-item shimmer"></div>
            <div class="shimmer-nav-item shimmer"></div>
          </div>
        </div>

        <!-- Left Sidebar (if applicable) -->
        <div *ngIf="hasLeftSidebar(selectedLayout)" class="shimmer-left-sidebar shimmer-element">
          <div class="shimmer-sidebar-item large shimmer"></div>
          <div class="shimmer-sidebar-item shimmer"></div>
          <div class="shimmer-sidebar-item small shimmer"></div>
        </div>

        <!-- Body -->
        <div class="shimmer-body shimmer-element">
          <div class="shimmer-content-block hero shimmer"></div>
          <div class="shimmer-content-block shimmer"></div>
        </div>

        <!-- Right Sidebar (if applicable) -->
        <div *ngIf="hasRightSidebar(selectedLayout)" class="shimmer-right-sidebar shimmer-element">
          <div class="shimmer-sidebar-item large shimmer"></div>
          <div class="shimmer-sidebar-item shimmer"></div>
          <div class="shimmer-sidebar-item small shimmer"></div>
        </div>

        <!-- Footer (if applicable) -->
        <div *ngIf="hasFooter(selectedLayout)" class="shimmer-footer shimmer-element">
          <div class="shimmer-footer-section">
            <div class="shimmer-footer-item wide shimmer"></div>
            <div class="shimmer-footer-item narrow shimmer"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


