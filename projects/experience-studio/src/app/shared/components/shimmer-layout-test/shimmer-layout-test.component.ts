import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface LayoutConfig {
  title: string;
  class: string;
  elements: string[];
}

@Component({
  selector: 'app-shimmer-layout-test',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './shimmer-layout-test.component.html',
  styleUrls: ['./shimmer-layout-test.component.scss']
})
export class ShimmerLayoutTestComponent implements OnInit {

  selectedLayout = 'hb';

  layouts: { [key: string]: LayoutConfig } = {
    'hb': {
      title: 'Header + Body Layout',
      class: 'hb-layout',
      elements: ['header', 'body']
    },
    'hbf': {
      title: 'Header + Body + Footer Layout',
      class: 'hbf-layout',
      elements: ['header', 'body', 'footer']
    },
    'hlsb': {
      title: 'Header + Left Sidebar + Body Layout',
      class: 'hlsb-layout',
      elements: ['header', 'left-sidebar', 'body']
    },
    'hlsbf': {
      title: 'Header + Left Sidebar + Body + Footer Layout',
      class: 'hlsbf-layout',
      elements: ['header', 'left-sidebar', 'body', 'footer']
    },
    'hbrs': {
      title: 'Header + Body + Right Sidebar Layout',
      class: 'hbrs-layout',
      elements: ['header', 'body', 'right-sidebar']
    },
    'hbrsf': {
      title: 'Header + Body + Right Sidebar + Footer Layout',
      class: 'hbrsf-layout',
      elements: ['header', 'body', 'right-sidebar', 'footer']
    },
    'hlsbrs': {
      title: 'Header + Left + Body + Right Sidebar Layout',
      class: 'hlsbrs-layout',
      elements: ['header', 'left-sidebar', 'body', 'right-sidebar']
    },
    'hlsbrsf': {
      title: 'Header + Left + Body + Right + Footer Layout',
      class: 'hlsbrsf-layout',
      elements: ['header', 'left-sidebar', 'body', 'right-sidebar', 'footer']
    }
  };

  constructor() { }

  ngOnInit(): void {
  }

  /**
   * Select a layout and update the display
   */
  selectLayout(layoutKey: string): void {
    this.selectedLayout = layoutKey;
  }

  /**
   * Get the current layout configuration
   */
  getCurrentLayout(): LayoutConfig {
    return this.layouts[this.selectedLayout];
  }

  /**
   * Check if layout has left sidebar
   */
  hasLeftSidebar(layoutKey: string): boolean {
    const layout = this.layouts[layoutKey];
    return layout ? layout.elements.includes('left-sidebar') : false;
  }

  /**
   * Check if layout has right sidebar
   */
  hasRightSidebar(layoutKey: string): boolean {
    const layout = this.layouts[layoutKey];
    return layout ? layout.elements.includes('right-sidebar') : false;
  }

  /**
   * Check if layout has footer
   */
  hasFooter(layoutKey: string): boolean {
    const layout = this.layouts[layoutKey];
    return layout ? layout.elements.includes('footer') : false;
  }

  /**
   * Get layout keys for iteration
   */
  getLayoutKeys(): string[] {
    return Object.keys(this.layouts);
  }

  /**
   * Get layout button label
   */
  getLayoutLabel(layoutKey: string): string {
    const labels: { [key: string]: string } = {
      'hb': 'HB - Header + Body',
      'hbf': 'HBF - Header + Body + Footer',
      'hlsb': 'HLSB - Header + Left Sidebar + Body',
      'hlsbf': 'HLSBF - Header + Left Sidebar + Body + Footer',
      'hbrs': 'HBRS - Header + Body + Right Sidebar',
      'hbrsf': 'HBRSF - Header + Body + Right Sidebar + Footer',
      'hlsbrs': 'HLSBRS - Header + Left + Body + Right Sidebar',
      'hlsbrsf': 'HLSBRSF - Header + Left + Body + Right + Footer'
    };
    return labels[layoutKey] || layoutKey.toUpperCase();
  }
}
