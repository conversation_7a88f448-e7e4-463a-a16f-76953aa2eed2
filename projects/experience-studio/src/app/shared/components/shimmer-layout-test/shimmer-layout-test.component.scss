// Shimmer Layout Test Component Styles

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #0a0a0a;
  color: #ffffff;
  min-height: 100vh;
}

.layout-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.layout-btn {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  &.active {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
  }
}

.layout-preview {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  min-height: 400px;
}

.layout-title {
  text-align: center;
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shimmer-layout-preview {
  width: 100%;
  height: 300px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

// Import shimmer styles from the main component
// Shimmer Animation
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer {
  position: relative;
  overflow: hidden;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.02) 0%, 
    rgba(255, 255, 255, 0.08) 50%, 
    rgba(255, 255, 255, 0.02) 100%);
  border-radius: 8px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.4) 50%, 
      transparent 100%);
    animation: shimmer 2s infinite;
  }
}

// Shimmer Grid Layouts
.shimmer-grid {
  display: grid;
  gap: 8px;
  min-height: 280px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.01);
  border-radius: 8px;

  // Layout Classes
  &.hb-layout {
    grid-template-areas: "header" "body";
    grid-template-rows: 30px 1fr;
  }

  &.hbf-layout {
    grid-template-areas: "header" "body" "footer";
    grid-template-rows: 30px 1fr 25px;
  }

  &.hlsb-layout {
    grid-template-areas: "header header" "sidebar body";
    grid-template-rows: 30px 1fr;
    grid-template-columns: 60px 1fr;
  }

  &.hlsbf-layout {
    grid-template-areas: "header header" "sidebar body" "footer footer";
    grid-template-rows: 30px 1fr 25px;
    grid-template-columns: 60px 1fr;
  }

  &.hbrs-layout {
    grid-template-areas: "header header" "body sidebar";
    grid-template-rows: 30px 1fr;
    grid-template-columns: 1fr 60px;
  }

  &.hbrsf-layout {
    grid-template-areas: "header header" "body sidebar" "footer footer";
    grid-template-rows: 30px 1fr 25px;
    grid-template-columns: 1fr 60px;
  }

  &.hlsbrs-layout {
    grid-template-areas: "header header header" "left-sidebar body right-sidebar";
    grid-template-rows: 30px 1fr;
    grid-template-columns: 50px 1fr 50px;
  }

  &.hlsbrsf-layout {
    grid-template-areas: "header header header" "left-sidebar body right-sidebar" "footer footer footer";
    grid-template-rows: 30px 1fr 25px;
    grid-template-columns: 50px 1fr 50px;
  }
}

// Shimmer Elements
.shimmer-element {
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.shimmer-header {
  grid-area: header;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
}

.shimmer-body {
  grid-area: body;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 6px;
}

.shimmer-footer {
  grid-area: footer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
}

.shimmer-left-sidebar {
  grid-area: left-sidebar;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px;
}

.shimmer-right-sidebar {
  grid-area: right-sidebar;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px;
}

// Shimmer Components
.shimmer-logo {
  width: 40px;
  height: 16px;
}

.shimmer-nav {
  display: flex;
  gap: 6px;
  margin-left: auto;
}

.shimmer-nav-item {
  width: 24px;
  height: 12px;
}

.shimmer-sidebar-item {
  height: 20px;

  &.small {
    height: 16px;
  }

  &.large {
    height: 28px;
  }
}

.shimmer-content-block {
  height: 24px;

  &.hero {
    height: 40px;
  }

  &.small {
    height: 16px;
  }
}

.shimmer-footer-section {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.shimmer-footer-item {
  height: 8px;

  &.wide {
    width: 40px;
  }

  &.narrow {
    width: 28px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .layout-selector {
    padding: 15px;
  }

  .layout-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .shimmer-grid {
    &.hlsb-layout,
    &.hlsbf-layout,
    &.hbrs-layout,
    &.hbrsf-layout,
    &.hlsbrs-layout,
    &.hlsbrsf-layout {
      grid-template-columns: 1fr !important;
      grid-template-areas: "header" "body" "footer" !important;
    }
    
    &.hlsb-layout {
      grid-template-rows: 30px 1fr !important;
    }
    
    &.hlsbf-layout,
    &.hbrsf-layout,
    &.hlsbrsf-layout {
      grid-template-rows: 30px 1fr 25px !important;
    }
    
    &.hbrs-layout {
      grid-template-rows: 30px 1fr !important;
    }
    
    &.hlsbrs-layout {
      grid-template-rows: 30px 1fr !important;
    }
  }

  .shimmer-left-sidebar,
  .shimmer-right-sidebar {
    display: none; // Hide sidebars on mobile
  }
}
